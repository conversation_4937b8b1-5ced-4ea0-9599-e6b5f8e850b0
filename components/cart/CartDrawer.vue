<template>
  <v-navigation-drawer
    :model-value="drawer"
    @update:model-value="closeDrawer"
    location="end"
    temporary
    touchless
    absolute
    width="550"
    style="overscroll-behavior: contain; overflow:hidden; position: fixed;"
  >
    <div class="d-flex justify-space-between align-center px-4 pt-3">
      <h2 class="text-h5">{{ $t("cart.label") }}</h2>
      <v-btn icon="ms:close" variant="text" @click="closeDrawer" />
    </div>
    <div class="drawer-content d-flex flex-column">
      <div
        class="drawer-scroll-content flex-grow-1 overflow-y-auto"
        v-if="orderStore.getItemNum > 0"
      >
        <div v-for="(item, index) in orderStore.order.order_items" :key="index">
          <v-divider v-if="index !== 0" class="ma-4" />

          <cart-product-item
            class="ma-4"
            :order-item="item"
            @update:order-item="updateOrderItem"
            @close-drawer="closeDrawer"
            :product-stock-info="productStockInfo"
            hide-status-error
          />
        </div>
        <!-- Other cart items would go here -->
      </div>

      <div
        v-else
        class="d-flex flex-grow-1 flex-column align-center justify-center"
        style="min-height: 300px"
      >
        <v-icon icon="ms:production_quantity_limits" size="64"></v-icon>
        <span class="text-h6 text-grey-darken-1"
          >{{ $t("checkout.no_items") }}
        </span>
        <span class="text-body-1 text-grey-darken-1"
          >{{ $t("checkout.add_items_prompt") }}
        </span>

        <v-btn
          class="mt-8 text-none"
          color="primary"
          :text="$t('checkout.go_to_menu')"
          variant="flat"
          v-bind="$ecomlink('/products')"
          @click="closeDrawer"
        />
      </div>

      <div class="cart-footer">
        <div class="d-flex justify-space-between align-center px-4 pb-1">
          <span class="text-h5">{{ $t("cart.total") }}</span>

          <span class="text-h5">{{
            formatCurrency(Number(orderStore.order.sub_total))
          }}</span>
        </div>
        <div class="text-caption text-center pb-2">
          {{ $t("cart.shipping_notice") }}
        </div>

        <div class="d-flex">
          <v-btn
            border
            blocked
            class="w-50"
            @click="
              route.path == '/checkout'
                ? navigateTo(localePath('/products/'))
                : closeDrawer()
            "
          >
            {{ $t("cart.empty_cart") }}
          </v-btn>
          <v-btn
            :disabled="orderStore.getItemNum == 0"
            blocked
            color="primary"
            class="flex-grow-1 ml-2"
            @click="navigateTo(localePath('/checkout'))"
          >
            {{ $t("cart.checkout") }}
          </v-btn>
        </div>
      </div>
    </div>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
const { t } = useI18n();
const route = useRoute();
const { $sfetch } = useNuxtApp();

const store = useMainStore();
const localePath = useLocalePath();
const orderStore = useOrderStore();
const emit = defineEmits(["update:drawer"]);
const props = defineProps({
  drawer: {
    type: Boolean,
    default: true,
  },
});

const productStockInfo = ref<CartStockItemInfo[]>([]);
const count_items = ref(0);

const checkItemStockInfo = async () => {
  try {
    const res: any = await $sfetch(
      "/ecommerce/checkout/validate-current-item-stock/",
      {
        method: "POST",
        body: {
          items: orderStore.order.order_items,
        },
      }
    );

    productStockInfo.value = res ?? [];
  } catch (e) {
    console.error(e);
  }
};

watch(
  () => orderStore.order.order_items,
  async (newVal) => {
    await checkItemStockInfo();
    count_items.value = newVal.length;
  },
  { immediate: true, deep: true }
);

const updateOrderItem = async (updatedItem: any) => {
  if (updatedItem.quantity === 0) {
    const confirm = await appConfirm({
      theme: "warning",
      title: t("app_confirm.remove_from_cart.title"),
      text: t("app_confirm.remove_from_cart.text"),
    });
    if (confirm) {
      orderStore.removeOrderItem(updatedItem.item_number);
    }
    return;
  }
  orderStore.updateCartItem(updatedItem);
};

const closeDrawer = () => {
  store.setDrawer(false);
};
</script>
<style scoped>
.drawer-content {
  height: calc(100% - 60px);
}

.cart-footer {
  background-color: white;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
}
</style>
