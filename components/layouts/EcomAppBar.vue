<template>
  <div>
    <v-app-bar
      v-if="
        settingsStore.get('ENABLE_ECOMMERCE_CONTACT_BAR') &&
        $vuetify.display.mdAndDown &&
        (settingsStore.get('ECOMMERCE_CONTACT_PHONE') ||
          settingsStore.get('ECOMMERCE_CONTACT_LINE'))
      "
      :color="settingsStore.get('ECOMMERCE_CONTACT_BAR_COLOR')"
      elevation="0"
      density="compact"
    >
      <v-container class="d-flex justify-space-around">
        <span
          v-if="settingsStore.get('ECOMMERCE_CONTACT_PHONE')"
          class="justify-center d-flex align-center text-subtitle-2"
          style="width: fit-content"
        >
          <v-icon class="me-1">ms:phone_in_talk</v-icon>

          <link-to-util
            :to="`tel:${settingsStore.get('ECOMMERCE_CONTACT_PHONE')}`"
            type="inline"
            :text="phoneNumber"
          />
        </span>

        <span
          v-if="settingsStore.get('ECOMMERCE_CONTACT_LINE')"
          class="justify-center d-flex align-center text-subtitle-2"
          style="width: fit-content"
        >
          <svg-icon
            :link="settingsStore.get('ECOMMERCE_CONTACT_LINE')"
            :name="line"
            class="mx-1"
          />
          <link-to-util
            :to="settingsStore.get('ECOMMERCE_CONTACT_LINE')"
            type="inline"
            :text="lineContact"
          />
        </span>
      </v-container>
    </v-app-bar>
    <v-app-bar border flat height="80" class="px-md-15">
      <div class="d-flex align-center justify-space-between w-100">
        <!-- Nav -->
        <div
          class="w-33"
          v-if="
            settingsStore.get('ENABLE_ECOMMERCE_LANUAGE_SETTING') ||
            settingsStore.get('ENABLE_ECOMMERCE_MENU_LINK')
          "
        >
          <v-app-bar-nav-icon
            rounded="circle"
            @click="side_bar_drawer = !side_bar_drawer"
          ></v-app-bar-nav-icon>
          <change-language
            v-if="settingsStore.get('ENABLE_ECOMMERCE_LANUAGE_SETTING')"
          />
          <v-btn
            v-if="settingsStore.get('ENABLE_ECOMMERCE_MENU_LINK')"
            v-bind="$ecomlink('/category')"
            class="px-1"
            style="width: 48px; font-size: 16px !important"
          >
            {{ $t("ecom_app_bar.menu") }}
          </v-btn>
        </div>
        <div
          :class="`w-33 d-flex justify-${settingsStore.get(
            'ECOMMERCE_LOGO_ALIGN'
          )}`"
        >
          <nuxt-link
            v-bind="$ecomlink(settingsStore.get('ECOMMERCE_HOME_PAGE_PATH'))"
            style="width: 120px"
          >
            <v-img
              :src="eComLogo"
              :alt="settingsStore.get('COMPANY_NAME')"
              max-width="120px"
              height="60px"
              contain
            ></v-img>
            <!-- <nuxt-img
              class="top-0 left-0 w-100 h-100"
              :src="eComLogo"
              :alt="settingsStore.get('COMPANY_NAME')"
              :format="'webp'"
              :quality="80"
              loading="eager"
              fetchpriority="high"
              style="
                 {
                }
              "
            ></nuxt-img> -->
          </nuxt-link>
        </div>

        <div class="w-33 d-flex align-center justify-end">
          <v-btn
            v-if="settingsStore.get('ENABLE_ECOMMERCE_SEARCH')"
            icon="ms:search"
            @click="search_dialog = !search_dialog"
            rounded="circle"
            :density="mobile ? 'comfortable' : 'default'"
          >
          </v-btn>
          <lazy-authentication-button />
          <v-btn
            icon
            @click="manageDrawer"
            v-if="settingsStore.get('ENABLE_ECOMMERCE_CART')"
            rounded="circle"
            :density="mobile ? 'comfortable' : 'default'"
          >
            <v-badge
              :content="cartItemsCount"
              :model-value="cartItemsCount > 0"
              color="error"
              floating
              :offset-x="mobile ? 10 : 5"
              offset-y="5"
            >
              <v-icon icon="ms:shopping_cart" />
            </v-badge>
          </v-btn>
        </div>
      </div>
    </v-app-bar>

    <v-app-bar border flat density="compact" v-if="smAndUp" class="px-md-15">
      <v-slide-group show-arrows>
        <v-slide-group-item v-for="category in pinCategories">
          <v-btn
            v-bind="$ecomlink(`/products?category=${category.url_name}`)"
            variant="plain"
            class="opacity-100"
          >
            {{ handleLanguage(category.name, category.eng_name) }}
          </v-btn>
        </v-slide-group-item>
      </v-slide-group>
    </v-app-bar>

    <lazy-ecom-side-bar v-model:drawer="side_bar_drawer" />
    <lazy-search-dialog v-model:drawer="search_dialog" />
    <lazy-unpiad-order />
  </div>
</template>

<script setup>
import { useSettingsStore } from "~/stores/setting";
import { useDisplay } from "vuetify";

const orderStore = useOrderStore();
const settingsStore = useSettingsStore();
const store = useMainStore();
const { mobile, smAndUp } = useDisplay();
const { handleLanguage } = useLanguage();

const search_dialog = ref(false);
const side_bar_drawer = ref(false);

const cartItemsCount = computed(() => {
  return orderStore.order.order_items.length;
});

const pinCategories = computed(() => {
  return settingsStore.product_categories.filter(
    (category) => category.is_pinned
  );
});

const eComLogo = computed(() => {
  return useImageKit(settingsStore.get("ECOMMERCE_NAVBAR_LOGO"), 200);
});

const lineContact = computed(() => {
  const link = settingsStore.get("ECOMMERCE_CONTACT_LINE");
  if (!link) {
    return "";
  }
  const regex = /@[\w\d]+/;
  const match = link.match(regex);
  if (match) {
    return match[0];
  }
  return "";
});

const manageDrawer = () => {
  store.setDrawer(!store.open_drawer);
};

const phoneNumber = computed(() => {
  let number = settingsStore.get("ECOMMERCE_CONTACT_PHONE");
  if (!number) {
    return null;
  }
  let numStr = number.toString().replace(/\D/g, "");

  // Remove Thailand country code if present
  if (numStr.startsWith("66")) {
    numStr = "0" + numStr.substring(2);
  }

  if (numStr.startsWith("02") && numStr.length === 9) {
    return numStr.replace(/(\d{2})(\d{3})(\d{4})/, "$1-$2-$3");
  } else if (!numStr.startsWith("02") && numStr.length === 10) {
    return numStr.replace(/(\d{3})(\d{3})(\d{4})/, "$1-$2-$3");
  } else {
    return null;
  }
});
</script>

<style scoped>
.scroll {
  overflow-y: auto;
  width: 100%;
  display: flex;
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}
</style>
