<template>
  <v-navigation-drawer
    :model-value="drawer"
    @update:model-value="emit('update:drawer', $event)"
  >
    <v-list density="compact">
      <template v-for="menu in navbar_items" :key="menu.title">
        <nav-item :menu="menu" :permissionsGroup="permissionsGroup" />
      </template>
    </v-list>

    <template v-slot:append>
      <slot name="append"></slot>
    </template>
  </v-navigation-drawer>
</template>

<script lang="ts" setup>
defineProps({
  drawer: Boolean,
});

const emit = defineEmits(["update:drawer"]);

const navbar_items = ref<NavItem[]>([
  {
    title: "หน้าหลัก",
    icon: "mdi-apps",
    path: "/menu/",
  },
  {
    title: "แดชบอร์ด",
    icon: "mdi-view-dashboard-outline",
    items: [
      {
        title: "ยอดขาย",
        path: "/report/dashboard2/",
        icon: "mdi-chart-line",
        permission: "full_access_dashboard",
      },
      {
        title: "การจัดส่ง",
        path: "/dashboard/shipping/",
        icon: "mdi-truck-fast-outline",
        permission: "full_access_dashboard",
      },
      {
        title: "เอกสาร",
        path: "/report/document-dashboard",
        permission: "can_view_report_document_dashboard",
        icon: "mdi-monitor-dashboard",
      },
    ],
  },
  {
    title: "คำสั่งซื้อ",
    icon: "mdi-cart-outline",
    items: [
      {
        title: "รายการคำสั่งซื้อ",
        path: "/order/list",
        permission: "full_access_order",
      },
      {
        title: "จุดทำออเดอร์",
        path: "/order/station",
        permission: "view_cooking_page",
      },
      {
        title: "นำเข้าคำสั่งซื้อ",
        path: "/order/import",
        permission: "can_import_order",
      },
    ],
  },
  {
    title: "เมนู",
    icon: "mdi-food-outline",
    permission: "full_access_product",
    items: [
      {
        title: "ประเภทเมนู",
        path: "/product-type",
        permission: "full_access_product",
      },
      {
        title: "หมวดหมู่เมนู",
        path: "/product-category/list",
        permission: "full_access_product",
      },
      {
        title: "เมนู",
        path: "/product/list",
        permission: ["add_seometa", "full_access_product"],
      },
      {
        title: "เมนูที่เป็นชุด",
        path: "/product-set/list",
        permission: "full_access_product",
      },
      {
        title: "ตัวเลือกเมนู",
        path: "/product-option/list",
        permission: "full_access_product",
      },
      {
        title: "ท็อปปิ้ง",
        path: "/topping-group/list",
        permission: "full_access_product",
      },
    ],
  },

  {
    title: "สต๊อก",
    icon: "mdi-cube-outline",
    items: [
      {
        title: "สต็อก",
        icon: "mdi-archive-outline",
        path: "/stock/material/list",
        permission: "full_access_material",
      },
      {
        title: "หมวดหมู่สต็อก",
        icon: "mdi-archive-outline",
        path: "/stock/category",
        permission: "full_access_material",
      },
      // {
      //   title: "Supplier",
      //   path: "/stock/supplier/list",
      // },

      {
        title: "นับสต๊อก",
        icon: "mdi-order-bool-descending-variant",
        path: "/stock/counting/list",
        permission: "full_access_counting_stock",
      },
      {
        title: "เพิ่มสต็อก",
        icon: "mdi-package-variant-closed-plus",
        path: "/stock/addition/list",
        permission: "full_access_addition_stock",
      },
      {
        title: "ตัดสต๊อก",
        icon: "mdi-package-variant-closed-minus",
        path: "/stock/deduction/list",
        permission: "full_access_deduction_stock",
      },
      {
        title: "ปรับสต๊อก",
        icon: "mdi-file-document-refresh-outline",
        path: "/stock/adjustment/list",
        permission: "full_access_adjustment_stock",
      },

      {
        title: "เบิกสต็อก",
        icon: "mdi-text-box-plus-outline",
        path: "/stock/material-request/list",
        permission: "full_access_material_request",
      },

      {
        title: "โอนสต็อก",
        icon: "mdi-file-arrow-left-right-outline",
        path: "/stock/transfer-stock/list",
        permission: "full_access_transfer_stock",
      },
      {
        title: "ส่งสต็อก",
        icon: "mdi-truck-delivery-outline",
        path: "/stock/delivery_note/list",
        permission: "full_access_deliverynote",
      },
      {
        title: "รับสต็อก",
        icon: "mdi-cube-scan",
        path: "/stock/material-receive/list",
        permission: "full_access_material_receive",
      },
      {
        title: "จัดการของเสีย",
        icon: "mdi-cookie-minus",
        path: "/stock/waste-stock/list",
        permission: "full_access_waste_stock",
      },
      {
        title: "แผนการผลิต",
        icon: "mdi-floor-plan",
        path: "/stock/production-plan/list",
        permission: "full_access_production_plan",
      },
      {
        title: "รายการสั่งซื้อ",
        icon: "mdi-shopping-search-outline",
        path: "/stock/purchase_order/",
        permission: "full_access_purchase_order",
      },
    ],
  },

  {
    title: "สมาชิก",
    icon: "mdi-account-circle-outline",
    items: [
      {
        title: "สมาชิก",
        path: "/customer/member/list",
        permission: "full_access_customer",
      },
      {
        title: "รางวัล",
        path: "/customer/reward/list",
        permission: "full_access_reward",
      },
      {
        title: "แลกของรางวัล",
        path: "/customer/redeem",
        permission: "full_access_redeem",
      },
      {
        title: "ระดับสมาชิก",
        path: "/customer/tier",
        permission: "view_membershiptier",
      },
    ],
  },
  {
    title: "ผู้ใช้",
    icon: "mdi-account-lock-open-outline",
    path: "/user/list",
    permission: ["full_access_user"],
  },
  {
    title: "รายงาน",
    icon: "mdi-chart-box-outline",
    items: [
      // sub menu
      {
        title: "ยอดขาย",
        icon: "mdi-cash-multiple",
        items: [
          {
            title: "ตามการชำระเงิน",
            path: "/report/main/payment_method",
            permission: "can_view_report_sale_by_payment_method",
          },
          {
            title: "ตามหมวดหมู่สินค้า",
            path: "/report/main/product-categories",
            permission: "can_view_report_sale_by_product_category",
          },
          {
            title: "ตามประเภทสินค้า",
            path: "/report/main/product-type",
            permission: "can_view_report_sale_by_product_type",
          },
          {
            title: "ตามสินค้าและสินค้าชุด",
            path: "/report/main/product",
            permission: "can_view_report_sale_by_product_and_set",
          },
          {
            title: "ตามช่องทางการขาย",
            path: "/report/main/sale-channel",
            permission: "can_view_report_sale_by_sale_channel",
          },
          {
            title: "ตามสาขา",
            path: "/report/main/branch",
            permission: "can_view_report_sale_by_branch",
          },
          {
            title: "ตามออเดอร์",
            path: "/report/main/bill",
            permission: "can_view_report_sale_by_order",
          },
          {
            title: "รายละเอียดออเดอร์",
            path: "/report/main/sale-bill-detail",
            permission: "can_view_report_sale_by_order_detail",
          },
          {
            title: "ยอดรวมตามวันที่",
            path: "/report/main/sale-by-date",
            permission: "can_view_report_sale_by_date",
          },
          {
            title: "ตามวันที่แยกช่องทางการขาย",
            path: "/report/main/sale-by-date-by-sale-channel",
            permission: "can_view_report_sale_by_date_by_sale_channel",
          },
          {
            title: "ตามวันที่แยกสาขา",
            path: "/report/main/sale-by-date-by-branch",
            permission: "can_view_report_sale_by_date_and_branch",
          },
          {
            title: "ตามวันในสัปดาห์",
            path: "/report/main/week-day-summary",
            permission: "can_view_report_sale_by_weekday",
          },
        ],
      },
      {
        title: "โปรโมชั่น",
        icon: "mdi-sale-outline",
        items: [
          {
            title: "โค้ดส่วนลด",
            path: "/report/promotion/discount-code",
            permission: "can_view_report_discount_code",
          },
          {
            title: "วงล้อรับของรางวัล",
            path: "/report/promotion/random-wheel-transaction",
            // permission: "can_view_report_promotion",
          },
        ],
      },
      {
        title: "สมาชิก",
        icon: "mdi-account-circle-outline",
        items: [
          //   {
          //     title: "ภาพรวมลูกค้า",
          //     path: "/report/main/drawer",
          //   },
          {
            title: "วิเคราะห์ลูกค้าเก่า/ใหม่",
            path: "/report/customer/overview",
            permission: "can_view_report_customer_new_old",
          },
          {
            title: "ความถี่การซื้อของลูกค้า",
            path: "/report/customer/purchase-frequency",
            permission: "can_view_report_customer_purchase_frequency",
          },
          {
            title: "สัดส่วนระดับสมาชิก",
            path: "/report/customer/member-ship-tier-ratio",
            permission: "can_view_report_customer_member_ship_tier",
          },
          {
            title: "ประวัติระดับสมาชิก",
            path: "/report/customer/member-ship-tier-history",
            permission: "can_view_report_customer_member_ship_tier_history",
          },
          {
            title: "สะสมแต้ม",
            path: "/report/customer/member-point-history",
            permission: "can_view_report_customer_point",
          },
          {
            title: "แลกแต้ม",
            path: "/report/customer/member-redeem-history",
            permission: "can_view_report_customer_redeem_point",
          },
          {
            title: "QR Code สะสมแต้ม",
            path: "/report/customer/award-point-transaction",
          },
        ],
      },
      {
        title: "ส่งของ",
        icon: "mdi-truck-fast-outline",
        items: [
          {
            title: "รายงานการส่งของ",
            path: "/report/shipping/detail",
            permission: "can_view_report_shipping_detail",
          },
        ],
      },
      {
        title: "สต็อก",
        icon: "mdi-cube-outline",
        items: [
          {
            title: "ความเคลื่อนไหวสต็อก",
            path: "/report/stock",
            permission: "can_view_report_stock",
          },
          {
            title: "สต็อกปัจจุบัน",
            path: "/report/stock/current-stock",
            permission: "can_view_report_stock_current_stock",
          },
          {
            title: "การเติมสินค้าเข้าคลัง",
            path: "/report/stock/addition-stock",
            permission: "can_view_report_stock_addition_stock",
          },
          {
            title: "สรุปการเติมสินค้าเข้าคลัง",
            path: "/report/stock/summary-addition-stock",
            permission: "can_view_report_stock_summary_addition_stock",
          },
          {
            title: "สรุปต้นทุนของเสีย",
            path: "/report/stock/waste-summary",
            permission: "can_view_report_stock_waste_summary",
          },
        ],
      },

      {
        title: "พนักงาน",
        icon: "mdi-account-circle-outline",
        items: [
          {
            title: "เข้าออกงาน",
            path: "/report/employee/check-in-out",
            permission: "can_view_report_employee_check_in_out",
          },
          {
            title: "เปิดปิดกะ",
            path: "/report/employee/drawer",
            permission: "can_view_report_employee_drawer",
          },
        ],
      },
      {
        title: "อื่นๆ",
        icon: "mdi-dots-vertical-circle-outline",
        path: "/report/main/check-in-out",
        items: [
          {
            title: "ประวัติการพิมพ์",
            path: "/report/other/printing-log",
            permission: "can_view_report_other_printing_log",
          },
          {
            title: "เชื่อมต่อระบบบัญชี",
            path: "/report/other/accounting-log",
            permission: "can_view_report_other_accounting_log",
          },
          {
            title: "รายงานการแจ้งเตือน",
            path: "/report/other/notify-log",
            permission: "can_view_report_notify_log",
          },
        ],
      },
    ],
  },
  {
    title: "ตั้งค่า Ecommerce",
    icon: "mdi-web",
    items: [
      {
        title: "หน้าแรก",
        path: "/ecommerce/page/home",
        permission: "add_page",
      },
      {
        title: "หน้าเว็บ",
        path: "/ecommerce/page",
        permission: "add_page",
      },
      {
        title: "บล็อคโพสต์",
        path: "/ecommerce/blog-post",
        permission: "add_ecommercepage",
      },
      {
        title: "แบรนด์",
        path: "/ecommerce/brand",
        permission: "add_brand",
      },
    ],
  },
  {
    title: "การตั้งค่า",
    icon: "mdi-cog-outline",
    items: [
      {
        title: "บริษัท",
        path: "/company/setting",
        permission: "full_access_company",
      },
      {
        title: "ช่องทางการขาย",
        path: "/sale-channel/list",
        permission: "full_access_sale_channel",
      },
      {
        title: "จุดทำออเดอร์",
        path: "/station",
        permission: "full_access_order_station",
      },
      {
        title: "การชำระเงิน",
        path: "/payment/list",
        permission: "full_access_payment_method",
      },
      {
        title: "สาขา",
        path: "/branch",
        permission: "full_access_branch",
      },
      {
        title: "คลัง",
        path: "/stock/inventory/list",
        permission: "full_access_inventory",
      },
      {
        title: "ขนส่ง",
        path: "/setting/delivery-provider",
        permission: "view_deliveryprovider",
      },
      {
        title: "โค้ดส่วนลด",
        path: "/setting/discount-code",
        permission: "add_discountcode",
      },
    ],
  },
  // {
  //   title: "บันทึกยอดขาย",
  //   icon: "mdi-cash-multiple",
  //   path: "/flowaccount/bulk_invoice",
  //   permission: "full_access_order",
  // },
  // {
  //   title: "ต้มผีเดือด",
  //   icon: "mdi-ghost-outline",
  //   path: "/ghost-hot-pot/add-coin",
  // },
]);

function permissionsGroup(menu: NavItem): boolean {
  if (menu.items?.length) {
    const had_perm = menu.items.filter((item) =>
      item?.permission ? hasSomePerm(item.permission) : true
    );
    return had_perm.length > 0 ? true : false;
  }

  if (menu.permission) {
    return hasPerm(menu.permission);
  }

  return false;
}
</script>

<style scoped></style>
