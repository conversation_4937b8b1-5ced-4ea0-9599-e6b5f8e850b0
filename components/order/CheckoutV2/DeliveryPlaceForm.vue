<template>
  <div>
    <p class="mb-2 mt-2">
      ข้อมูลที่อยู่ลูกค้า
      <span
        class="text-primary text-decoration-underline"
        @click="getCustomerData()"
      >
        ดึงข้อมูลจากลูกค้า
      </span>
    </p>
    <shipping-address-form
      ref="shipping_form"
      :shipping-address="shippingSet.end_address"
      @update:shipping-address="updateShipping({ end_address: $event })"
      :suggestions="catch_address_set"
    />

    <p class="mb-2 mt-4">ข้อมูลขนส่ง</p>
    <v-row dense class="mb-4">
      <v-col cols="6">
        <delivery-provider-select
          hide-details="auto"
          density="compact"
          :rules="[rules.required]"
          item-value="id"
          :model-value="shippingSet.delivery_provider"
          @update:model-value="updateShipping({ delivery_provider: $event })"
        ></delivery-provider-select>
      </v-col>
      <v-col cols="6">
        <v-select
          :model-value="shippingSet.delivery_service_type"
          @update:model-value="
            updateShipping({ delivery_service_type: $event })
          "
          :items="[
            { title: 'รถยนต์', value: 'CAR' },
            { title: 'มอเตอร์ไซค์', value: 'MOTORCYCLE' },
          ]"
          hide-details="auto"
          density="compact"
          label="ประเภทขนส่ง"
        >
        </v-select
      ></v-col>
      <v-col cols="12" class="d-flex ga-2 align-center">
        <v-text-field
          label="เวลาที่ลูกค้าต้องการรับ"
          type="datetime-local"
          density="compact"
          hide-details="auto"
          :model-value="order.pickup_datetime"
          @update:model-value="updatePreOrder($event)"
        ></v-text-field>
        <v-btn
          color="success"
          flat
          size="small"
          variant="tonal"
          icon="mdi-calculator"
          @click="calculateSchedule()"
          :loading="loading_delivery_cost"
        >
        </v-btn>
        <v-text-field
          label="เวลาที่ขนส่งมารับของ"
          type="datetime-local"
          density="compact"
          hide-details="auto"
          :model-value="shippingSet.schedule_at"
          @update:model-value="updateShipping({ schedule_at: $event })"
        ></v-text-field>
      </v-col>

      <v-col cols="12" class="d-flex ga-2 align-center">
        <v-text-field
          hide-details="auto"
          density="compact"
          label="ค่าส่งที่ลูกค้าชำระ"
          suffix="บาท"
          type="number"
          bg-color="yellow-lighten-4"
          :model-value="deliveryFee"
          @update:model-value="updateDeliveryFee($event)"
        ></v-text-field>
        <v-text-field
          hide-details="auto"
          density="compact"
          label="ต้นทุนขนส่ง"
          suffix="บาท"
          type="number"
          :model-value="shippingSet.delivery_cost"
          @update:model-value="updateShipping({ delivery_cost: Number($event) })"
          :rules="[rules.min(0)]"
        ></v-text-field>
      </v-col>
      <v-col v-if="delivery_cost_detail">
        <v-banner
          class="mx-auto"
          border
          rounded="lg"
          color="warning"
          icon="mdi-truck-fast-outline"
        >
          <p>
            <b>ค่าจัดส่งอาจมีการเปลี่ยนแปลง</b>
            <br />
            ค่าจัดส่ง:
            <b>{{ useCurrency(delivery_cost_detail.delivery_cost) }} บาท</b>
            <br />
            ระยะทาง:
            {{ (delivery_cost_detail.distance_meter / 1000).toFixed(2) }}
            กม.
            <br />
            ระยะเวลา:
            {{ formatSecond(delivery_cost_detail.estimate_second) }}
            <br />
            หมายเลขอ้างอิง: {{ delivery_cost_detail.quotation_id }}
          </p>
        </v-banner>
      </v-col>

      <v-col cols="12">
        <p class="mt-4">ข้อมูลขนส่งอื่นๆ</p>
      </v-col>
      <v-col cols="6">
        <v-text-field
          label="หมายเลขงาน"
          density="compact"
          hide-details="auto"
          :model-value="shippingSet.job_id"
          @update:model-value="updateShipping({ job_id: $event })"
        ></v-text-field>
      </v-col>
      <v-col cols="6">
        <v-text-field
          label="link ติดตามขนส่ง"
          density="compact"
          hide-details="auto"
          :model-value="shippingSet.tracking_link"
          @update:model-value="updateShipping({ tracking_link: $event })"
        ></v-text-field>
      </v-col>
    </v-row>
  </div>
</template>

<script setup lang="ts">
import { useMainStore } from "~/store";
import _ from "lodash";
import moment from "moment";

const { formatSecond } = useFormatter();
const { sfetch } = useServerFetch();
const mainStore = useMainStore();

const emit = defineEmits([
  "update:shippingSet",
  "update:order",
  "update:deliveryFee",
]);
const props = defineProps({
  shippingSet: {
    type: Object as PropType<OrderShipping>,
    required: true,
  },
  deliveryFee: {
    type: [Number, String],
    default: 0,
  },
  order: {
    type: Object as PropType<Order>,
    required: true,
  },
});

const shipping_form = ref();
const loading_delivery_cost = ref(false);
const delivery_cost_detail = ref<CalculateDeliveryCost>();
const catch_address_set = ref([]);

function updateShipping(data: Partial<OrderShipping>) {
  emit("update:shippingSet", {
    ...props.shippingSet,
    ...data,
  });
}

function updatePreOrder(value: string) {
  emit("update:order", {
    ...props.order,
    pickup_datetime: value,
    is_pre_order: value != null,
  });
}

function updateDeliveryFee(value: number) {
  console.log("updateDeliveryFee", value);
  emit("update:deliveryFee", Number(value));
}

function getCustomerData() {
  if (props.order.customer_detail.name) {
    let update_data = {
      name: props.order.customer_detail.name,
      phone:
        props.order.customer_detail.country_calling_code +
        props.order.customer_detail.phone_number,
    };

    if (props.order.customer_detail.address_set.length > 0) {
      // find default address first if not found get first address
      catch_address_set.value = props.order.customer_detail.address_set;
      let default_address = _.find(catch_address_set.value, { default: true });

      if (default_address) {
        update_data = default_address;
      } else {
        update_data = props.order.customer_detail.address_set[0];
      }
      shipping_form.value.addMarker(update_data.lat, update_data.lng);
    }

    updateShipping({
      end_address: update_data,
    });
  }
}

async function calculateSchedule() {
  const { valid } = await shipping_form.value.validate();
  if (!valid) return;

  if (!props.shippingSet.end_address.input_address) {
    appConfirm({
      title: "ไม่สามารถคำนวณค่าจัดส่งได้",
      text: "กรุณากรอกข้อมูลที่อยู่ลูกค้า",
      theme: "error",
      confirmOnly: true,
    });
    return;
  }

  let end_lat = props.shippingSet.end_address.lat;
  let end_lng = props.shippingSet.end_address.lng;
  if (end_lat == 0 || end_lng == 0) {
    const geocoding = await shipping_form.value.getGeocoding(
      props.shippingSet.end_address
    );
    end_lat = geocoding.lat;
    end_lng = geocoding.lng;
  }

  const branch_address = mainStore.branch.settings.BRANCH_ADDRESS;
  console.log("Branch_address", branch_address);
  if (!branch_address.lat || !branch_address.lng) {
    appConfirm({
      title: "ไม่สามารถคำนวณค่าจัดส่งได้",
      text: "ไม่พบตำแหน่งของสาขา กรุณาตั้งค่าตำแหน่งของสาขา ตั้งค่า > สาขา > ตำแหน่งสาขา",
      theme: "error",
    });
    return;
  }
  loading_delivery_cost.value = true;
  try {
    const response = await sfetch<CalculateDeliveryCost>(
      "/shipping/calculate-delivery-cost/",
      {
        method: "POST",
        body: {
          start_lat: branch_address.lat,
          start_lng: branch_address.lng,
          start_address: branch_address.input_address,
          end_lat: end_lat,
          end_lng: end_lng,
          end_address: props.shippingSet.end_address.input_address,
          delivery_at: moment()
            .add(2, "days")
            .format("YYYY-MM-DDTHH:mm:ss.SSSZ"),
          service_type: props.shippingSet.delivery_service_type,
        },
      }
    );
    delivery_cost_detail.value = response;

    const update_data = {
      delivery_cost: response.delivery_cost,
      distance: Number((response.distance_meter / 1000).toFixed(2)),
      estimate_delivery_time: response.estimate_second,
    };

    if (props.order.pickup_datetime) {
      update_data["schedule_at"] = moment(props.order.pickup_datetime)
        .subtract(response.estimate_second, "seconds")
        .format("YYYY-MM-DDTHH:mm");
    }

    updateShipping(update_data);
  } catch (e) {
    alertError(e);
  }
  loading_delivery_cost.value = false;
}
</script>

<style scoped></style>
