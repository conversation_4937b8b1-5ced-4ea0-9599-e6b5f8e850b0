<template>
  <v-card
    title="ใบเบิกของ"
    rounded="lg"
    v-if="material_requests && material_requests.length > 0"
  >
    <template v-slot:prepend>
      <v-icon icon="mdi-package-variant-closed" color="primary"></v-icon>
    </template>

    <v-card-text>
      <v-row v-for="(mr, index) in material_requests" :key="mr.id" class="mb-2">
        <v-col cols="12">
          <v-card
            variant="flat"
            density="compact"
            class="border rounded-lg"
            :title="mr.document_no"
          >
            <template #append>
              <v-btn
                variant="text"
                color="primary"
                size="small"
                density="compact"
                icon="mdi-file-document-outline"
                :to="`/stock/material-request/${mr.id}/detail/`"
                target="_blank"
              >
              </v-btn>
            </template>

            <v-card-text class="d-flex flex-wrap">
              <div class="me-4">
                <span class="text-medium-emphasis">สถานะ</span>
                <v-chip
                  class="ms-2"
                  size="x-small"
                  :color="getStatusColor(mr.status)"
                >
                  {{ getStatusText(mr.status) }}
                </v-chip>
              </div>
              <div class="me-4">
                <span class="text-medium-emphasis">ผู้ขอเบิก</span>
                <span
                  class="ms-1"
                  >{{ mr.create_by_set?.first_name || '-' }}</span
                >
              </div>
              <!-- <div class="me-4">
                <span class="text-medium-emphasis">คลังต้นทาง</span>
                <span
                  class="ms-1"
                  >{{ mr.request_inventory_set?.name || '-' }}</span
                >
              </div> -->
              <div class="me-4">
                <span class="text-medium-emphasis">เบิกจาก</span>
                <span class="ms-1">{{ mr.to_inventory_set?.name || '-' }}</span>
              </div>
              <div class="me-4">
                <span class="text-medium-emphasis">วันที่สร้าง</span>
                <span class="ms-1">{{ datetime(mr.create_date) }}</span>
              </div>
              <div class="me-4" v-if="mr.receive_datetime">
                <span class="text-medium-emphasis">วันที่นัดรับ</span>
                <span class="ms-1">{{ datetime(mr.receive_datetime) }}</span>
              </div>
              <div v-if="mr.remark">
                <span class="text-medium-emphasis">หมายเหตุ</span>
                <br />
                <p class="pa-1 border">{{ mr.remark }}</p>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
        <v-divider v-if="index < material_requests.length - 1"></v-divider>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { PropType } from 'vue';
const props = defineProps({
  material_requests: {
    type: Array as PropType<any[]>,
    required: true
  },
  showItems: {
    type: Boolean,
    default: true
  }
});

const { datetime } = useFormatter();

function getStatusColor(status: string) {
  switch (status) {
    case 'draft':
      return 'warning';
    case 'confirmed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'grey';
  }
}

function getStatusText(status: string) {
  switch (status) {
    case 'draft':
      return 'รอยืนยัน';
    case 'confirmed':
      return 'สำเร็จ';
    case 'cancelled':
      return 'ยกเลิก';
    default:
      return 'ไม่ทราบสถานะ';
  }
}
</script>

<style scoped>
.border {
  border: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
