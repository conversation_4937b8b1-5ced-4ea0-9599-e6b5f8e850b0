<template>
  <v-card rounded="lg" elevation="2" :title="title">
    <v-card-text>
      <div v-if="loading" class="text-center mt-3">
        <v-progress-circular indeterminate></v-progress-circular>
        <h3 class="mt-3">กำลังประมวลผลรายงาน</h3>
      </div>
      <div v-else-if="chartData.datasets && chartData.datasets.length > 0">
        <component
          :is="chartComponent"
          :chartData="chartData"
          :options="chartOptions"
          :height="height"
        />
      </div>
      <h3 v-else class="text-center mt-3">ไม่มีข้อมูล</h3>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>hnut<PERSON><PERSON> } from "vue-chart-3";
import { Chart, ChartData, ChartOptions, registerables } from "chart.js";

Chart.register(...registerables);

const props = defineProps({
  title: {
    type: String,
    default: 'Chart'
  },
  apiUrl: {
    type: String,
    required: true
  },
  filter: {
    type: Object,
    default: () => ({})
  },
  chartType: {
    type: String,
    default: 'bar', // bar, line, pie, doughnut
    validator: (value: string) => ['bar', 'line', 'pie', 'doughnut'].includes(value)
  },
  height: {
    type: Number,
    default: 350
  },
  options: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['data-loaded']);

const { sfetch } = useServerFetch();

const loading = ref(false);
const chartData = ref<ChartData<any>>({
  labels: [],
  datasets: []
});

const chartComponent = computed(() => {
  switch (props.chartType) {
    case 'line':
      return LineChart;
    case 'pie':
      return PieChart;
    case 'doughnut':
      return DoughnutChart;
    case 'bar':
    default:
      return BarChart;
  }
});

const chartOptions = computed<ChartOptions<any>>(() => {
  const defaultOptions: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top'
      },
      tooltip: {
        mode: 'index',
        intersect: false,
      }
    }
  };

  // Merge with custom options
  return {
    ...defaultOptions,
    ...props.options
  };
});

async function fetchReport() {
  loading.value = true;
  try {
    const response = await sfetch(props.apiUrl, {
      method: 'POST',
      body: props.filter
    });
    
    // Expect backend to return chart.js compatible data structure
    if (response.chart_data) {
      chartData.value = response.chart_data;
    } else {
      // Fallback: if backend returns raw data, try to format it
      chartData.value = {
        labels: response.labels || [],
        datasets: response.datasets || []
      };
    }
    
    emit('data-loaded', response);
  } catch (error) {
    console.error('Error fetching chart data:', error);
    chartData.value = { labels: [], datasets: [] };
  } finally {
    loading.value = false;
  }
}

watch(() => props.filter, () => {
  fetchReport();
}, { deep: true });

onMounted(() => {
  fetchReport();
});

defineExpose({
  fetchReport
});
</script>

<style scoped>
</style>
