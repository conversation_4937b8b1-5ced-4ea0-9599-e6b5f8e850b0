<template>
  <v-card rounded="lg" elevation="2" :title="title">
    <v-card-text>
      <div v-if="loading" class="text-center mt-3">
        <v-progress-circular indeterminate></v-progress-circular>
        <h3 class="mt-3">กำลังประมวลผลรายงาน</h3>
      </div>
      <div v-else-if="chartData.datasets && chartData.datasets.length > 0">
        <BarChart
          :chartData="chartData"
          :options="chartOptions"
          :height="height"
        />
      </div>
      <h3 v-else class="text-center mt-3">ไม่มีข้อมูล</h3>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { BarChart } from "vue-chart-3";
import { ChartData, ChartOptions } from "chart.js";


const props = defineProps({
  title: {
    type: String,
    default: 'Chart'
  },
  apiUrl: {
    type: String,
    required: true
  },
  filter: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: Number,
    default: 350
  },
  options: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['data-loaded']);

const { sfetch } = useServerFetch();

const loading = ref(false);
const chartData = ref<ChartData<any>>({
  labels: [],
  datasets: []
});
const dailyTotals = ref<number[]>([]);

const chartOptions = computed<ChartOptions<any>>(() => {
  const defaultOptions: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
        title: {
          display: true,
          text: 'ค่าส่ง (บาท)'
        }
      }
    },
    plugins: {
      legend: {
        display: true,
        position: 'bottom'
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          footer: function(tooltipItems: any) {
            let sum = 0;
            tooltipItems.forEach(function(tooltipItem: any) {
              sum += tooltipItem.parsed.y;
            });
            return 'รวม: ' + sum.toLocaleString() + ' บาท';
          }
        }
      },
      datalabels: {
        display: false
      }
    }
  };

  // Merge with custom options
  return {
    ...defaultOptions,
    ...props.options
  };
});

async function fetchReport() {
  loading.value = true;
  try {
    const response: any = await sfetch(props.apiUrl, {
      method: 'POST',
      body: props.filter
    });

    // Expect backend to return chart.js compatible data structure
    if (response.chart_data) {
      chartData.value = response.chart_data;
      dailyTotals.value = response.chart_data.daily_totals || [];
    }

    emit('data-loaded', response);
  } catch (error) {
    console.error('Error fetching chart data:', error);
    chartData.value = { labels: [], datasets: [] };
    dailyTotals.value = [];
  } finally {
    loading.value = false;
  }
}

defineExpose({
  fetchReport
});
</script>

<style scoped></style>
