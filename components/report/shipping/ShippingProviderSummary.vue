<template>
  <div class="provider-summary">
    <v-table density="compact">
      <thead>
        <tr>
          <th>ขนส่ง</th>
          <th class="text-center">ออเดอร์</th>
          <th class="text-center">ต้นทุนค่าส่ง</th>
        </tr>
      </thead>
      <tbody>
        <tr 
          v-for="provider in providerData" 
          :key="provider.delivery_provider__name"
          class="provider-row"
          @click="$emit('provider-click', provider.delivery_provider__name)"
        >
          <td class="provider-name">
            {{ provider.delivery_provider__name || 'ไม่ระบุ' }}
          </td>
          <td class="text-center">
            {{ formatNumber(provider.total_orders) }}
          </td>
          <td class="text-center">
            {{ formatCurrency(provider.total_cost) }}
          </td>
        </tr>
        <tr v-if="!providerData || providerData.length === 0">
          <td colspan="3" class="text-center text-grey">
            ไม่มีข้อมูล
          </td>
        </tr>
      </tbody>
    </v-table>
  </div>
</template>

<script setup lang="ts">
interface ProviderData {
  delivery_provider__name: string;
  total_orders: number;
  total_cost: number;
}

const props = defineProps({
  providerData: {
    type: Array as PropType<ProviderData[]>,
    default: () => []
  }
});

const emit = defineEmits(['provider-click']);

const { formatNumber, formatCurrency } = useFormatter();
</script>

<style scoped>
.provider-summary {
  max-height: 400px;
  overflow-y: auto;
}

.provider-row {
  cursor: pointer;
  transition: background-color 0.2s;
}

.provider-row:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.provider-name {
  font-weight: medium;
}

th {
  background-color: #f5f5f5;
  font-weight: bold;
}
</style>
