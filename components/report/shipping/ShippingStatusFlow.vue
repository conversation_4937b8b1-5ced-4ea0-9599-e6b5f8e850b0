<template>
  <div class="status-flow-container">
    <div v-if="loading" class="text-center mt-3">
      <v-progress-circular indeterminate></v-progress-circular>
      <h3 class="mt-3">กำลังประมวลผลรายงาน</h3>
    </div>
    <div v-else class="status-flow">
      <template v-for="(status, index) in statusFlow" :key="status.code">
        <div class="status-item" @click="$emit('status-click', status.code)">
          <!-- Status Circle -->
          <div
            class="status-circle"
            :class="[`bg-${status.color}`, { 'clickable': true }]"
          >
            <div class="status-count">{{ getStatusCount(status.code) }}</div>
          </div>

          <!-- Status Label -->
          <div class="status-label">{{ status.label }}</div>
        </div>

        <!-- Arrow (except for last item) -->
        <div v-if="index < statusFlow.length - 1" class="status-arrow">
          <v-icon>mdi-arrow-right</v-icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
const { sfetch } = useServerFetch();

interface StatusData {
  [key: string]: number;
}
const statusData = ref<StatusData>({});
const loading = ref(false);

const emit = defineEmits(['status-click']);

// Status flow configuration matching the image
const statusFlow = [
  { code: 'VOID', label: 'ยกเลิก', color: 'red' },
  { code: 'PENDING', label: 'รอยืนรก', color: 'yellow' },
  { code: 'REQUEST', label: 'กำลังยืนรก', color: 'warning' },
  { code: 'IN_COMING', label: 'เริ่มรกแล้ว', color: 'warning' },
  { code: 'ON_GOING', label: 'รับของแล้ว', color: 'info' },
  { code: 'SUCCESS', label: 'ขนส่งสำเร็จ', color: 'success' },
];

function getStatusCount(statusCode: string): number {
  return statusData.value[statusCode] || 0;
}


async function fetchReport(filter_data:any) {
  loading.value = true;
  try {
    const response: any = await sfetch('/report/shipping_dashboard/aggregated-report/', {
      method: 'POST',
      body: filter_data
    });
    statusData.value = response.data?.status_counts || {};
  } catch (error) {
    console.error('Error fetching status data:', error);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  fetchReport,
});
</script>

<style scoped>
.status-flow-container {
  padding: 20px;
  overflow-x: auto;
}

.status-flow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 800px;
  gap: 20px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: transform 0.2s;
}

.status-item:hover {
  transform: scale(1.05);
}

.status-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  transition: box-shadow 0.2s;
}

.status-circle:hover {
  box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.status-count {
  font-size: 24px;
  font-weight: bold;
}

.status-label {
  text-align: center;
  font-size: 14px;
  font-weight: medium;
  color: #424242;
  max-width: 100px;
  word-wrap: break-word;
}

.status-arrow {
  top: 35px;
  color: #9e9e9e;
  font-size: 24px;
}

.clickable {
  cursor: pointer;
}

@media (max-width: 768px) {
  .status-flow {
    flex-direction: column;
    gap: 30px;
  }

  .status-arrow {
    transform: rotate(90deg);
    right: auto;
    top: 90px;
  }
}
</style>
