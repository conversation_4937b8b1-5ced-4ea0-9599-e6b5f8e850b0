<template>
  <v-card
    style="height: 100%; min-width:200px"
    :color="layout.color"
    rounded="lg"
  >
    <div class="d-flex justify-space-between text-center px-md-3 px-sm-2">
      <div class="w-100 py-3 px-3">
        <p
          class="font-weight-medium text-medium-emphasis"
          style="font-size: medium"
        >
          {{ layout?.title }}
        </p>
        <h2 class="text-high-emphasis">
          {{ formatValue(value, layout.suffix) }}
        </h2>
      </div>
    </div>
  </v-card>
</template>
<script setup lang="ts">
import { IAggregatedReport } from "~/interface/report_framework";

defineProps({
  layout: {
    type: Object as PropType<IAggregatedReport>,
    required: true,
  },
  value: {
    type: [String, Number],
    default: "",
  },
  toolBar: {
    type: Boolean,
    default: false,
  },
});
const $emit = defineEmits(["update:trigger-btn"]);

function formatValue(value: any, suffix: string = "") {
  // Attempt to parse the value as a float

  if (!isNaN(value) || Number.isInteger(value)) {
    // Format the number as a price and apply the useCurrency function
    const formattedValue = parseFloat(value).toFixed(2);
    if (suffix === "บาท") {
      return useCurrency(formattedValue) + " บาท";
    } else {
      return useCurrency(formattedValue, 0) + " " + suffix;
    }
  } else {
    // Return the value as a string
    return String(value) + suffix;
  }
}
</script>
