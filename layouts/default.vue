<template>
  <v-app>
    <lazy-cart-drawer
      v-if="settingsStore.get('ENABLE_ECOMMERCE_CART')"
      v-model:drawer="mainStore.open_drawer"
    />

    <ecom-app-bar />
    <!-- utility -->
    <v-main>
      <slot />
    </v-main>
    <ecom-footer />

    <lazy-loader-util />
    <lazy-consent-dialog v-model:show-dialog="showDialog" />
    <lazy-app-snack-bar />
    <lazy-alert-util />
  </v-app>
</template>

<script setup>
const store = useSettingsStore();
const orderStore = useOrderStore();
const mainStore = useMainStore();
const authStore = useAuthStore();
const settingsStore = useSettingsStore();
const route = useRoute();
const token = useCookie("token");
const config = useRuntimeConfig();
const showDialog = ref(false);

function setupOmise() {
  Omise.setPublicKey(store.get("OMISE_API_PUBLIC_KEY"));
}

onMounted(() => {
  checkUserConsent();
  authStore.setupMitt();
  orderStore.getOrderLocalStorage();
  if (route.query.state || route.query.code) {
    authStore.setLineProfile();
  } else if (token.value) {
    authStore.setAccessToken(token.value);
  }
});

watch(() => mainStore.open_drawer, (value) => {
  if (value) {
    const scrollY = window.scrollY;
    document.documentElement.style.position = 'fixed';
    document.documentElement.style.top = `-${scrollY}px`;
    document.documentElement.classList.add('cart-drawer--open');
  } else {
    const savedY = parseInt(document.documentElement.style.top || '0') * -1;
    document.documentElement.style.position = '';
    document.documentElement.style.top = '';
    document.documentElement.classList.remove('cart-drawer--open');
    window.scrollTo(0, savedY);
  }
});

function checkUserConsent() {
  showDialog.value = localStorage.getItem("consent_status") !== "true";
}

// const {data} = await useAsyncData("fetchSettings", store.fetchSettings);
const { data } = await useSFetch("/ecommerce/company/get-settings/");

if (data.value) {
  settingsStore.settings = data.value.settings;
  settingsStore.product_categories = data.value.categories;
}
const settings = data.value.settings;
orderStore.fetchCategories();

// Base webpage schema
useSchemaOrg([
  defineWebSite({
    name: settings["COMPANY_NAME"],
    url: config.public.baseUrl,
  }),
  defineOrganization({
    "@id": `${config.public.baseUrl}/#organization`,
    name: settings["COMPANY_NAME"],
    url: config.public.baseUrl,
    logo: settingsStore.get("ECOMMERCE_NAVBAR_LOGO"),
  }),
  defineWebPage({
    "@id": `${config.public.baseUrl}${route.fullPath}#webpage`,
    name: settings["COMPANY_NAME"],
    url: `${config.public.baseUrl}${route.fullPath}`,
    isPartOf: {
      "@id": `${config.public.baseUrl}/#website`,
    },
    about: {
      "@id": `${config.public.baseUrl}/#organization`,
    },
  }),
]);

const canonicalUrl = `${config.public.baseUrl}${route.fullPath}`;
useHead({
  titleTemplate: `%s | ${settings["COMPANY_NAME"]}`,
  link: [{ rel: "canonical", href: canonicalUrl }],
  script: [
    {
      src: "https://cdn.omise.co/omise.js",
      async: true,
      onload: setupOmise,
    },
  ],
});
</script>

<style>
.layout-enter-active,
.layout-leave-active {
  transition: all 0.4s;
}

.layout-enter-from,
.layout-leave-to {
  filter: grayscale(1);
}

.cart-drawer--open {
  /* 1) hide any overflow on the root element */
  overflow: hidden;
  /* 2) lock the height to 100% of the viewport,
       so that even programmatic scrolls can’t sneak in */
  height: 100vh;
}

/* OPTIONAL: if you want to prevent “bounce” overscroll on iOS */
.cart-drawer--open {
  overscroll-behavior: contain;
}
</style>
