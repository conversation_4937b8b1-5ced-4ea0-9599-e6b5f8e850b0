<template>
  <bread-crumbs :breadcrumbs="breadcrumbs" />
  <DocumentLayout
    title="เเก้ไขใบเบิกของ"
    ref="doc_form"
    :breadcrumbs="breadcrumbs"
  >
    <template #action_header>
      <div style="width: 180px">
        <v-text-field
          label="เลขที่เอกสาร"
          placeholder="กรอกเลขที่"
          :rules="[rules.required]"
          v-model="document_data.document_no"
          hide-details="auto"
          readonly
        >
        </v-text-field>
      </div>
    </template>

    <template #header>
      <header-stock
        v-if="material_request"
        :headers="header_title"
        v-model:data="document_data"
      >
        <template v-slot:request_inventory>
          <inventory-select
            :model-value="document_data.request_inventory"
            outlined
            :rules="[rules.required]"
            hide-details="auto"
            label="คลังที่ขอเบิก"
            readonly
          >
          </inventory-select>
        </template>
        <template v-slot:to_inventory>
          <inventory-select
            :model-value="document_data.to_inventory"
            :exclude_inventory="document_data.request_inventory"
            @update:model-value="setMainStock($event)"
            outlined
            :rules="[rules.required]"
            hide-details="auto"
            label="คลังที่ให้เบิก"
          >
          </inventory-select>
        </template>
      </header-stock>
    </template>

    <template #body>
      <super-table
        :headers="headers"
        :hideNewRow="true"
        v-model:data="current_stocks"
        v-model:errors="input_errors"
        :search_able="['material.name', 'material.sku', 'unit_name']"
        :row_color_logic="rowColorLogic"
        show-filter
        row-key="uuid"
        max-height="50vh"
      />
    </template>

    <template #footer_action>
      <v-btn
        to="/stock/material-request/list"
        flat
        :width="130"
        size="large"
        rounded
        >ยกเลิก</v-btn
      >
      <v-btn
        @click="openDialog()"
        :width="130"
        size="large"
        flat
        color="success"
        class="ms-3"
        rounded
        >ยืนยัน</v-btn
      >
    </template>
  </DocumentLayout>

  <confirm-create
    v-model:dialog="dialog"
    v-model:document_data="document_data"
    :header_title="header_title"
    :summary_table="summary_table"
    :default_table="material_request.material_request_items"
    @click:save="save()"
  />
</template>

<script setup>
definePageMeta({
  permission: "full_access_material_request",
})

import { useMainStore } from "~/store";
import _ from "lodash";
const { $hasPerm } = useNuxtApp();
const { sfetch } = useServerFetch();
const store = useMainStore();
// Tools
const input_errors = ref([]);
const route = useRoute();

// Document
const doc_form = ref(null);
const breadcrumbs = computed(() => [
  { title: "ใบเบิกของ", to: "/stock/material-request/list" },
  { title: "แก้ไขใบเบิกของ", disabled: true },
]);
const header_title = ref([
  {
    title: "ข้อมูลเอกสาร",
    main: true,
  },
  {
    title: "สร้างโดย",
    key: "create_by_set.username",
    read_only: true,
    cols: 3,
  },

{
  key: "receive_datetime",
  type: "datetime-local",
  title: "ต้องการรับของวันที่",
  cols: 3,
},
{
  key: "document_no",
  type: "text",
  cols: 3,
},
{
  key: "is_reserved",
  title: "จองสต็อก",
  type: "checkbox",
  cols: 3,
},
  {
    title: "คลังสินค้าที่สั่ง",
    key: "request_inventory",
    type: "text",
    cols: 3,
  },
  {
    title: "คลังสินค้าที่รับคำสั่ง",
    key: "to_inventory",
    type: "text",
    cols: 3,
  },
]);
const document_data = ref({
  document_no: "",
  create_by: getUser(),
  request_inventory: null,
  to_inventory: null,
  remark: "",
});

// Supertable
const headers = ref([
  {
    type: "string",
    title: "รายการวัตถุดิบ",
    key: "material.name",
    read_only: true,
    width: "200px",
  },
  {
    type: "string",
    title: "หน่วย",
    key: "unit_name",
    read_only: true,
    align: "end",
    width: "90px",
  },
  {
    type: "number",
    title: "ขั้นต่ำ",
    key: "min_stock",
    read_only: true,
    align: "end",
    width: "100px",
  },
  {
    type: "number",
    title: "จน. สต็อก",
    key: "current_stock_amount",
    read_only: true,
    align: "end",
    width: "120px",
  },
  {
    type: "number",
    title: "พร้อมขาย (ครัว)",
    key: "main_stock_amount",
    read_only: true,
    align: "end",
    width: "120px",
  },
  {
    type: "number",
    title: "จน.จอง (ครัว)",
    key: "main_reserved_stock_amount",
    read_only: true,
    align: "end",
    width: "120px",
  },
  {
    type: "count",
    title: "จํานวนที่เบิก",
    key: "request_amount",
    width: "100px",
    rules: [rules.min(0)],
  },
]);

// Stock
const current_stocks = ref([]);
const summary_table = ref([]);
const material_request = ref({});

// Dialog
const dialog = ref(false);


onMounted(async () => {

  /**
   * @param {Inventory} inventory1 - คลังขอเบิก
   * @param {Inventory} inventory2 - คลังรับเบิก
   */

  const material_request = await getMaterialRequest();
  document_data.value = material_request;

  let aggregated_data_table = await getInventory1Stock(
    material_request.request_inventory
  );

  aggregated_data_table = setRequestInventoryStockWithMininum(
    aggregated_data_table,
    material_request.request_inventory_set.selected_material
  );

  aggregated_data_table = setRequestItems(
    aggregated_data_table,
    material_request.material_request_items
  );

  const inventory2_items = await getCurrentStock(material_request.to_inventory);
  setMainInventoryStock(aggregated_data_table, inventory2_items);

  current_stocks.value = prepareRows(aggregated_data_table);
});

async function openDialog() {
  for (let cur_stock of current_stocks.value) {
    if (cur_stock.request_amount < 0) {
      appAlert({
        theme: "error",
        title: `ห้ามเบิกสินค้าเป็นจำนวนติดลบ`,
      });
      return;
    }
  }

  summary_table.value = current_stocks.value.filter(
    (cs) => cs.request_amount > 0
  );
  dialog.value = true;
}

function setRequestInventoryStockWithMininum(
  inventory1_items,
  selected_material
) {
  const inv1_items = _.cloneDeep(inventory1_items);
  const selected_mat_dict = _.keyBy(selected_material, "id");
  for (let req_unit of inv1_items) {
    if (selected_mat_dict[req_unit.id]) {
      req_unit.min_stock = selected_mat_dict[req_unit.id].min_stock;
    }
  }
  return inv1_items;
}

function setRequestItems(inventory1_items, material_request_items) {
  const inv1_items = _.cloneDeep(inventory1_items);
  const mat_req_dict = _.keyBy(material_request_items, "unit");
  for (let req_unit of inv1_items) {
    if (mat_req_dict[req_unit.id]) {
      req_unit.request_amount = mat_req_dict[req_unit.id].quantity;
    }
  }
  return inv1_items;
}

function setMainInventoryStock(inv1_items, inv2_items) {
  const inv2_items_dict = _.keyBy(inv2_items, "id");
  for (let inv1_item of inv1_items) {
    if (inv2_items_dict[inv1_item.id]) {
        let material_lots = inv2_items_dict[inv1_item.id].material_lots
        inv1_item.main_stock_amount = _.sumBy(material_lots, mat_lots => mat_lots.quantity || 0) ;
        inv1_item.main_reserved_stock_amount = _.sumBy(material_lots, mat_lots => mat_lots.reserved_quantity || 0);
      }
    else {
      inv1_item.main_stock_amount = 0;
    }
  }
}

function rowColorLogic(item) {
  const request_amount = item.request_amount;
  const material_request_items = _.keyBy(
    material_request.value.material_request_items,
    "unit"
  );

  if (material_request_items[item.id] === undefined) {
    return;
  }

  if (material_request_items[item.id].quantity != request_amount) {
    return "bg-light-green-lighten-4";
  }
}

async function getMaterialRequest() {
  const res = await sfetch(
    `/stock/material_request/material_request/${route.params.id}/`,
    {
      params: {
        "include[]": [
          "material_request_items",
          "create_by_set.*",
          "request_inventory_set.*"
        ],
      },
    }
  );
  material_request.value = res.material_request;
  return res.material_request;
}

async function setMainStock(inventory2_id) {
  document_data.value.to_inventory = inventory2_id;
  const inventory2_items = await getCurrentStock(inventory2_id);
  setMainInventoryStock(current_stocks.value, inventory2_items);
}

async function getInventory1Stock(inventory_id) {
  // prepare selected material
  const selected_mats = await getSelectedMaterial(inventory_id);
  const selected_mats_dict = _.keyBy(selected_mats, "id");

  // GET current_stocks
  const current_stocks = await getCurrentStock(inventory_id);

  // Loop to set min_stock
  for (let index = 0; index < current_stocks.length; index++) {
    const cs_item = current_stocks[index];
    cs_item.min_stock = selected_mats_dict[cs_item.id].min_stock;
    cs_item.current_stock_amount = cs_item.total_amount;
  }

  const sorted_items = _.sortBy(
    current_stocks,
    [(x) => Number(x.material.order_index)],
    ["asc"]
  );
  return sorted_items;
}

async function getCurrentStock(inventory_id) {
  try {
    const all_materials = await sfetch(
      "stock/material_request/get_current_stock/",
      {
        params: {
          inventory_id: inventory_id,
        },
      }
    );

    for (const material of all_materials) {
      let stock_amount = 0;
      for (const material_lot of material.material_lots) {
        stock_amount += material_lot.quantity;
      }
      material.total_amount = stock_amount;
    }
    return all_materials;
  } catch (e) {
    if (e.response) {
      alertHttpError(e);
    } else {
      console.error(e);
    }
  }
}

async function getSelectedMaterial(inventory_id) {
  try {
    const response = await sfetch(
      `/stock/core/resource/inventories/${inventory_id}/`
    );
    return response.inventory.selected_material;
  } catch (e) {
    if (e.response) {
      alertHttpError(e);
    } else {
      console.error(e);
    }
  }
}

async function save() {
  // prepare payload / body
  for (const st in summary_table.value) {
    if (st.item_id) {
      st.id = st.item_id;
    } else {
      delete st.id;
    }
  }

  const payload = {
    document_no: document_data.value.document_no,
    request_inventory: document_data.value.request_inventory,
    to_inventory: document_data.value.to_inventory,
    remark: document_data.value.remark,
    receive_datetime: document_data.value.receive_datetime,
    is_reserved: document_data.value.is_reserved,
    material_request_items: summary_table.value.map((x) => {
      return {
        id: x.id,
        material: x.material.id,
        material_info: x.material,
        unit: x.id,
        unit_info: {
          id:x.id,
          unit_name: x.unit_name,
          cost: x.cost,
          sm_unit_amount: x.sm_unit_amount,
          min_stock: x.min_stock,
          is_active: x.is_active,
          sm_unit: x.sm_unit,
          company: x.company,
          current_stock_amount: x.current_stock_amount,
          main_stock_amount: x.main_stock_amount,
        },
        quantity: x.request_amount,
      };
    }),
  };

  try {
    const res = await sfetch(
      `/stock/material_request/update/${route.params.id}/`,
      {
        method: "PUT",
        body: payload,
      }
    );

    navigateTo("/stock/material-request/list");
  } catch (e) {
    alertHttpError(e);
  }
}
</script>
