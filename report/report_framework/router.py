from rest_framework.routers import Route, DynamicRoute, SimpleRouter


class ReportRouter(SimpleRouter):
    routes = [
        Route(
            url=r"^{prefix}/table-report{trailing_slash}$",
            mapping={"post": "get_report"},
            name="{basename}-get_report",
            detail=False,
            initkwargs={},
        ),
        Route(
            url=r"^{prefix}/filter-schema{trailing_slash}$",
            mapping={"get": "get_filter_schema"},
            name="{basename}-get_filter_schema",
            initkwargs={},
            detail=False,
        ),
        Route(
            url=r"^{prefix}/aggregated-report{trailing_slash}$",
            mapping={"post": "get_aggregated_report"},
            name="{basename}-get_aggregated_report",
            initkwargs={},
            detail=False,
        ),
        Route(
            url=r"^{prefix}/chart-data{trailing_slash}$",
            mapping={"post": "chart_data"},
            name="{basename}-chart_data",
            initkwargs={},
            detail=False,
        ),
        # Route(
        #     url=r'^{prefix}/{lookup}$',
        #     mapping={'get': 'retrieve'},
        #     name='{basename}-detail',
        #     detail=True,
        #     initkwargs={'suffix': 'Detail'}
        # ),
        DynamicRoute(
            url=r"^{prefix}/{url_path}{trailing_slash}$",
            name="{basename}-{url_name}",
            detail=True,
            initkwargs={},
        ),
    ]
