from django.urls import path
from report.api_views.dashboard.api_views import (
    DashboardAPIView,
    DocumentDashboardAPIView,
)
from report.api_views.sales import api_views as sales_api_views
from report.api_views.customer import api_views as customer_api_views
from report.api_views.customer import analytic_customer_dashboard
from report.api_views.promotion import api_views as random_wheel_views
from report.api_views.sales.daily_drawer_by_branch import (
    DrawerPaymentReportExcelV2,
    DrawerReportExcelView,
)
from report.api_views.sales import discount_code as discount_code_views
from report.api_views.shipping.shipping_detail import (
    ShippingDetailReportData,
    ShippingDashboardReportData,
)
from report.api_views.stock import api_views as stock_views
from report.api_views.other import api_views as other_views
from report.api_views.stock.addition_stock_views import AdditionStockReport
from report.api_views.stock.summary_addition_stock_views import (
    SummaryAdditionStockReport,
)
from report.api_views.product_sales_detail import api_views as product_daily_sale
from report.api_views.stock.waste_summary import WasteSummaryReport
from report.report_framework.router import ReportRouter

report_router = ReportRouter()

# =======================
# Experimental
# =======================
report_router.register("experimental", sales_api_views.ExperimentReport)

# =======================
# Stock
# =======================
report_router.register("stock/stock_usage_by_unit", stock_views.StockUsageByUnitReport)
report_router.register(
    "stock/stock_usage_by_unit_detail", stock_views.StockUsageByUnitDetailReport
)
report_router.register("stock/stock_transaction", stock_views.StockTransactionReport)
report_router.register("stock/current_stock", stock_views.CurrentStockReport)
report_router.register("stock/addition_stock", AdditionStockReport)
report_router.register("stock/summary_addition_stock", SummaryAdditionStockReport)
report_router.register("stock/waste_summary", WasteSummaryReport)

# =======================
# Sales
# =======================
report_router.register(
    "payment_report",
    sales_api_views.PaymentDataReport,
)
report_router.register(
    "branch_report",
    sales_api_views.BranchDataReport,
)
report_router.register(
    "product_category_report",
    sales_api_views.ProductCategoriesDataReport,
)
report_router.register(
    "product_type_report",
    sales_api_views.ProductTypeReportData,
)
report_router.register(
    "product",
    sales_api_views.ProductReportData,
)
report_router.register(
    "bill",
    sales_api_views.SaleBillReportData,
)
report_router.register(
    "bill_detail",
    sales_api_views.SaleBillDetailReportData,
)
report_router.register(
    "sale_channel",
    sales_api_views.SaleChannelReportData,
)
report_router.register(
    "sale_by_date",
    sales_api_views.SaleDateReportData,
)
report_router.register(
    "sale_by_date_by_sale_channel",
    sales_api_views.SaleDateBySaleChannelReportData,
)
report_router.register(
    "sale_by_date_by_branch",
    sales_api_views.SaleDateByBranchReportData,
)
report_router.register(
    "sale_by_hour",
    sales_api_views.SaleHourReportData,
)
report_router.register(
    "week_day_summary",
    sales_api_views.WeekDaySummaryReport,
)
report_router.register(
    "bill_summary",
    sales_api_views.BillSummaryReportData,
)
# Report Product Detail
report_router.register(
    "daily_product_sale",
    product_daily_sale.ProductDailySaleReport,
)

# =======================
# Promotion
# =======================
report_router.register(
    "promotion/discount_code_summary",
    discount_code_views.DiscountCodeSummaryReport,
)
report_router.register(
    "promotion/discount_code_detail",
    discount_code_views.DiscountCodeDetailReport,
)
report_router.register(
    "promotion/random_wheel_transaction",
    random_wheel_views.RandomWheelTransactionReport,
)

# =======================
# Shipping
# =======================
report_router.register("shipping_detail", ShippingDetailReportData)
report_router.register("shipping_dashboard", ShippingDashboardReportData)
# =======================
# Employee
# =======================
report_router.register("check_in_out", sales_api_views.CheckinCheckoutReportData)
report_router.register("drawer", sales_api_views.DrawerReportData)
# =======================
# Customer
# =======================
report_router.register(
    "customer/member_ship_tier_ratio",
    customer_api_views.MemberShipTierRatioReport,
)
report_router.register(
    "customer/member_ship_tier_history",
    customer_api_views.MemberShipTierHistoryReport,
)
report_router.register(
    "customer/point_history",
    customer_api_views.PointHistoryReport,
)
report_router.register(
    "customer/redeem_history",
    customer_api_views.RedeemHistoryReport,
)
report_router.register(
    "analytic_customer/overview",
    analytic_customer_dashboard.AnalyticDailyCustomer,
)
report_router.register(
    "analytic_customer/overview-summary",
    analytic_customer_dashboard.AnalyticDailySummaryCustomer,
)
report_router.register(
    "analytic_customer/purchase-frequency",
    analytic_customer_dashboard.PurchaseFrequencyReport,
)
report_router.register(
    "customer/award_point_transaction",
    customer_api_views.AwardPointTransactionReport,
)
# =======================
# Other
# =======================
report_router.register("other/printing_log", other_views.PrintingLogReport)
report_router.register("other/accounting_log", other_views.AccountingLogReport)
report_router.register("other/notify_log", other_views.NotifyReport)
report_router.register("other/notify_log_table", other_views.NotifyTableReport)


urlpatterns = [
    path("dashboard/", DashboardAPIView.as_view()),
    path("dashboard/doc/", DocumentDashboardAPIView.as_view()),
    path("sales/customer/", sales_api_views.TestCustomerReport.as_view()),
    path("sales/daily_drawer_by_branch/", DrawerReportExcelView.as_view()),
    path("sales/daily_drawer_by_branch/v2/", DrawerPaymentReportExcelV2.as_view()),
] + report_router.urls
